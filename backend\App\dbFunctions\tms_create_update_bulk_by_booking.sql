CREATE OR REPLACE FUNCTION public.tms_create_update_bulk_by_booking(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    -- Bare minimums
    status boolean := false;
    message text := 'Internal_error';
    affected_rows integer;
    validation_resp text[];
    resp_data json;
    ip_address_ text;
    user_agent_ text;
    org_id_ integer;
    usr_id_ uuid;
    srvc_type_id_ integer;
    srvc_config_data_ json;
    
    -- Form data
    batch_data_ json;
    _form_data_proto json;

    -- temp
    _single_entry json;
    _form_data_proto_json json;
    _single_entry_creation_resp json;
    row_count_ int;
    validation_resp_ json;
    org_level_settings_data json;
    _config_data json;

    -- Booking specific
    selected_date_ date;
    requested_slot text;
    requested_start_ts timestamp;
    requested_end_ts timestamp;
    srvc_req_pincode text;
    hub_id_ int;
    skill_id_ int;
    capacity_result json;
    booking_result json;
    capacity_available boolean := false;
    capacity_id_ bigint;
    booking_details_ jsonb;
    booking_slots_ jsonb;
    service_hub int;
    vertical_id_ int;
    selected_prvdr int;
    is_auto_booking bool;
    current_date_ date ;
    ins_ids bigint[];
    _entry_ids_vs_query json default '{}';

    -- Final output arrays
    successfully_created jsonb[] := '{}';
    failed_to_book jsonb[] := '{}';
    validation_errors jsonb[] := '{}';
BEGIN
    -- Extract metadata
    org_id_ := (form_data_->>'org_id')::int;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    is_auto_booking := (form_data_->>'is_auto_booking')::bool;
    vertical_id_ := (form_data_->>'vertical_id')::int;
    srvc_type_id_ := form_data_->>'srvc_type_id';
    batch_data_ := form_data_->'batch_data';
    _form_data_proto := form_data_::jsonb - 'batch_data';
    current_date_ =  now() at time zone 'utc';
    skill_id_ := (form_data_->>'skill_id')::int;
    row_count_ := 0;

    -- Get config and org settings
    SELECT form_data INTO _config_data FROM cl_cf_service_types WHERE service_type_id = srvc_type_id_;
    org_level_settings_data := tms_hlpr_get_org_level_settings_config_data_for_org(org_id_)->'data';

    FOR _single_entry IN SELECT * FROM json_array_elements(batch_data_)
    LOOP
        row_count_ := row_count_ + 1;
        srvc_req_pincode := _single_entry->>'cust_pincode';
        requested_slot := _single_entry->>'booking_slot';
        selected_date_ := (_single_entry->>'bulk_booking_day')::date;
        selected_prvdr := (_single_entry->>'bulk_booking_prvdr')::int;
        -- Init
        capacity_available := false;
        hub_id_ := NULL;
        capacity_id_ := NULL;
        booking_details_ := '{}'::jsonb;
        _form_data_proto_json = '{}'::jsonb;
		_form_data_proto_json = _form_data_proto::jsonb || _single_entry::jsonb;
	    _form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb, '{srvc_type_id}', to_jsonb(srvc_type_id_), true);
	   
        _form_data_proto_json = jsonb_set(_form_data_proto_json::jsonb, '{new_prvdr}', to_jsonb(selected_prvdr), true);
       -- raise notice '_form_data_proto_json1 %',_form_data_proto_json;
        begin
	        
            -- Parse slot to UTC
            IF requested_slot IS NOT NULL THEN
                requested_start_ts := ((selected_date_::text || ' ' || trim(split_part(requested_slot, ' - ', 1)))::timestamp AT TIME ZONE 'Asia/Kolkata' AT TIME ZONE 'UTC');
                requested_end_ts := ((selected_date_::text || ' ' || trim(split_part(requested_slot, ' - ', 2)))::timestamp AT TIME ZONE 'Asia/Kolkata' AT TIME ZONE 'UTC');
            END IF;

            -- Get hub
            IF srvc_req_pincode IS NOT NULL AND length(srvc_req_pincode) >= 6 THEN
                SELECT hub.id INTO service_hub
                  FROM cl_tx_vertical_srvc_hubs AS hub
                 WHERE srvc_req_pincode = ANY(hub.pincodes)
                   AND hub.is_active = TRUE
                   AND hub.vertical_id = vertical_id_;
               --  raise notice 'service_hub %',service_hub;
                IF service_hub IS NOT NULL THEN
                    hub_id_ := service_hub;

                    -- Fetch capacity
                    if is_auto_booking is true then 
                       selected_date_:=current_date_;
                     --  raise notice 'is_auto_booking %',is_auto_booking;
                       capacity_result := tms_ace_get_capacity_by_resource(
                            concat_ws('_', selected_prvdr, vertical_id_::int, skill_id_, hub_id_),
                            selected_date_::text,
                            selected_date_::text
                        );
                    --   raise notice 'capacity_result %',capacity_result;
                    else
                        capacity_result := tms_ace_get_capacity_by_resource(
                            concat_ws('_', selected_prvdr, vertical_id_::int, skill_id_, hub_id_),
                            selected_date_::text,
                            selected_date_::text
                        );
                    end if;

                    IF capacity_result->'data'->'data' IS NOT NULL THEN
                        IF requested_slot IS NOT NULL THEN
                            SELECT elem INTO booking_result
                            FROM json_array_elements(capacity_result->'data'->'data') AS elem
                            WHERE (elem->>'startTime')::timestamp = requested_start_ts
                              AND (elem->>'endTime')::timestamp = requested_end_ts
                            LIMIT 1;
                        ELSE
                            SELECT elem INTO booking_result
                            FROM json_array_elements(capacity_result->'data'->'data') AS elem
                            LIMIT 1;
                        END IF;
						--raise notice 'booking_result %',booking_result;
                        IF booking_result IS NOT NULL THEN
                            capacity_available := TRUE;
                            capacity_id_ := (booking_result->>'capacityId')::bigint;

                            booking_slots_ := jsonb_build_object(
                                to_char(selected_date_, 'YYYY-MM-DD'),
                                jsonb_build_array(
                                    to_char((booking_result->>'startTime')::timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam') || ' - ' ||
                                    to_char((booking_result->>'endTime')::timestamp AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'HH12:MIam')
                                )
                            );

                            booking_details_ := jsonb_build_object(
                                'selected_date', selected_date_,
                                'capacity_id', capacity_id_,
                                'booked_slots', booking_slots_
                            );
                             _form_data_proto_json := jsonb_set(_form_data_proto_json ::jsonb, '{capacity_id}', to_jsonb(capacity_id_), true);
                             _form_data_proto_json := jsonb_set(_form_data_proto_json ::jsonb, '{booking_data}', booking_details_, true);

                        ELSE
                            booking_details_ := jsonb_build_object('error', 'No available capacity', 'booking_available', false);
                        END IF;
                    ELSE
                        booking_details_ := jsonb_build_object('error', 'Failed to fetch capacity', 'booking_available', false);
                    END IF;
                ELSE
                    booking_details_ := jsonb_build_object('error', 'No hub found for pincode', 'booking_available', false);
                END IF;
            ELSE 
                    booking_details_ := jsonb_build_object('error', 'No pincode provided', 'booking_available', false);
            END IF;

            -- Merge with proto
           

--          
            -- Validate
           -- raise notice '_form_data_proto_json %',_form_data_proto_json;
            validation_resp_ := tms_hlpr_srvc_req_validation(_config_data, org_level_settings_data, _form_data_proto_json);
            IF (validation_resp_->>'status')::boolean IS NOT TRUE THEN
                validation_errors := validation_errors || jsonb_build_object('request_data', _single_entry, 'error', validation_resp_->>'message');
                CONTINUE;
            END IF;

            -- Create
           _single_entry_creation_resp := tms_create_service_request(_form_data_proto_json);

            IF (_single_entry_creation_resp->>'status')::boolean THEN
                ins_ids := ins_ids || (_single_entry_creation_resp->'data'#>>'{entry_id}')::bigint;
                _entry_ids_vs_query := _entry_ids_vs_query::jsonb || json_build_object((_single_entry_creation_resp->'data'#>>'{entry_id}'), _form_data_proto_json)::jsonb;
                IF capacity_available THEN
                    successfully_created := successfully_created || jsonb_build_object(
                        'srvc_req_id', _single_entry_creation_resp->'data'->>'entry_id',
                        'display_code', _single_entry_creation_resp->'data'->>'display_code',
                        'booking_details', booking_details_,
                        'request_data',_single_entry
                    );
                ELSE
                    failed_to_book := failed_to_book || jsonb_build_object(
                        'srvc_req_id', _single_entry_creation_resp->'data'->>'entry_id',
                        'display_code', _single_entry_creation_resp->'data'->>'display_code',
                        'error',  booking_details_->>'error',
                        'request_data',_single_entry
                    );
                END IF;
            ELSE
                validation_errors := validation_errors || jsonb_build_object(
                    'request_data', _single_entry,
                    'error', 'Creation failed: ' || _single_entry_creation_resp->>'code'
                );
            END IF;

        EXCEPTION WHEN OTHERS THEN
            validation_errors := validation_errors || jsonb_build_object(
                'request_data', _single_entry,
                'error', 'Exception: ' || SQLERRM
            );
        END;
    END LOOP;

    resp_data := json_build_object(
        'summary', json_build_object(
            'total_requests', json_array_length(batch_data_),
            'successfully_created', array_length(successfully_created, 1),
            'failed_to_book', array_length(failed_to_book, 1),
            'validation_errors', array_length(validation_errors, 1)
        ),
        'successfully_created', successfully_created,
        'failed_to_book', failed_to_book,
        'validation_errors', validation_errors,
        'entry_ids',ins_ids,
        'entry_ids_vs_query',_entry_ids_vs_query
    );

    RETURN json_build_object('status', true, 'message', 'Processed successfully', 'data', resp_data);

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object('status', false, 'message', 'Unexpected error: ' || SQLERRM, 'data', null);
END;
$function$
;
