import React, { Component } from 'react';
import { <PERSON>lapse, Select, Button, Spin, Col } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import BookingMode from './BookingMode';
import ConfigHelpers from '../../util/ConfigHelpers';

const { Option } = Select;

class BulkCreationCollapse extends Component {
    constructor(props) {
        super(props);
        this.state = {
            selectedBulkPrvdr: null,
            capacityData: undefined,
            bookingModeModal: false,
            loadingCapacity: false,
        };
    }

    componentDidMount() {
        // Auto-select service provider if user is a service provider
        if (ConfigHelpers.isServiceProvider()) {
            const currentUserOrgId =
                ConfigHelpers.getUserDetailsInLocalStorage()?.org?.id;
            if (currentUserOrgId) {
                this.setState({
                    selectedBulkPrvdr: currentUserOrgId,
                });
                this.fetchCapcityData(currentUserOrgId);
            }
        } else {
            // Check for default provider configuration
            const defaultProvider =
                this.props.srvcConfigData?.srvc_default_provider;
            if (
                defaultProvider &&
                defaultProvider !== '' &&
                defaultProvider !== null
            ) {
                // Auto-select default provider if configured
                this.setState({
                    selectedBulkPrvdr: parseInt(defaultProvider),
                });
                this.fetchCapcityData(parseInt(defaultProvider));
            } else {
                // Auto-select "No Provider" if no default provider is configured
                this.setState({
                    selectedBulkPrvdr: 'no_provider',
                });
            }
        }
    }

    fetchCapcityData = (prvdrId) => {
        // Skip capacity API call for "no_provider" selection
        if (prvdrId === 'no_provider') {
            this.setState({
                loadingCapacity: false,
                capacityData: undefined,
                showBulkBookingModal: false,
            });
            return;
        }

        this.setState({ loadingCapacity: true });
        let params = {
            srvc_prvdr_id: prvdrId,
        };

        const onComplete = (resp) => {
            this.setState({
                loadingCapacity: false,
                capacityData: resp.data,
                showBulkBookingModal:
                    resp.data?.enable_capacity_module || false,
            });
        };

        const onError = (error) => {
            this.setState({
                loadingCapacity: false,
                capacityData: undefined,
                showBulkBookingModal: false,
            });
        };

        // Use the http_utils from props or import it
        this.props.httpUtils.performGetCall(
            '/booking/capacity',
            params,
            onComplete,
            onError
        );
    };

    getAllSrvcPrvdrs() {
        const { allSrvcPrvdrs, srvcConfigData } = this.props;

        // Get only allowed providers
        const allowedPrvdrs = allSrvcPrvdrs.filter((prvdr) =>
            srvcConfigData?.srvc_possible_prvdrs.includes(prvdr.value)
        );

        // Add "No Provider" option
        const noProviderOption = {
            value: 'no_provider',
            label: 'No Service Provider',
        };

        const defaultProviderId = parseInt(
            srvcConfigData?.srvc_default_provider
        );

        // Check if default provider exists in allSrvcPrvdrs
        const defaultPrvdr =
            allSrvcPrvdrs.find((p) => p.value === defaultProviderId) || null;

        // If default provider is valid but not in allowed list, add it manually
        const isDefaultInList = allowedPrvdrs.some(
            (prvdr) => prvdr.value === defaultProviderId
        );

        if (defaultPrvdr && !isDefaultInList) {
            allowedPrvdrs.push(defaultPrvdr);
        }

        return [noProviderOption, ...allowedPrvdrs];
    }

    render() {
        const {
            TMS250620631024,
            srvcConfigData,
            submitUrl,
            dataProto,
            orgSettingsData,
            onDataModified,
            srvcTypeId,
            viewData,
        } = this.props;

        const {
            selectedBulkPrvdr,
            capacityData,
            bookingModeModal,
            loadingCapacity,
        } = this.state;

        return (
            <Col xs={24} className="gx-my-1">
                <Collapse>
                    <Collapse.Panel
                        header={
                            <span className="gx-text-primary">
                                <UploadOutlined className="gx-mr-2" />
                                Click here for Bulk creation
                            </span>
                        }
                    >
                        <div>
                            {TMS250620631024 &&
                            srvcConfigData?.srvc_possible_prvdrs?.length > 0 ? (
                                <div>
                                    <div className="gx-mb-3">
                                        <label>Select Service Provider:</label>
                                        <Select
                                            className="gx-mt-2 gx-w-100"
                                            placeholder="Choose a service provider"
                                            value={selectedBulkPrvdr}
                                            disabled={ConfigHelpers.isServiceProvider()}
                                            onChange={(value) => {
                                                this.setState({
                                                    selectedBulkPrvdr: value,
                                                });
                                                // Fetch capacity data for all selections (including "no_provider")
                                                this.fetchCapcityData(value);
                                            }}
                                        >
                                            {this.getAllSrvcPrvdrs()?.map(
                                                (prvdr) => (
                                                    <Option
                                                        key={prvdr.value}
                                                        value={prvdr.value}
                                                    >
                                                        {prvdr.label}
                                                    </Option>
                                                )
                                            )}
                                        </Select>
                                        {loadingCapacity ? (
                                            <Spin />
                                        ) : (
                                            <>
                                                {selectedBulkPrvdr ===
                                                'no_provider' ? (
                                                    // Show BulkUploader directly for "no_provider" selection
                                                    <BulkUploader
                                                        onDataModified={
                                                            onDataModified
                                                        }
                                                        submitUrl={submitUrl}
                                                        dataProto={dataProto}
                                                        orgSettingsData={
                                                            orgSettingsData
                                                        }
                                                        timeFormatMsg
                                                        noProviderMode={true}
                                                    />
                                                ) : (
                                                    <>
                                                        {capacityData?.enable_capacity_module ? (
                                                            <Button
                                                                className="gx-mt-3"
                                                                type="primary"
                                                                onClick={() => {
                                                                    this.setState(
                                                                        {
                                                                            bookingModeModal: true,
                                                                        }
                                                                    );
                                                                }}
                                                            >
                                                                Check Booking
                                                                Mode
                                                            </Button>
                                                        ) : (
                                                            <>
                                                                {selectedBulkPrvdr && (
                                                                    <BulkUploader
                                                                        onDataModified={
                                                                            onDataModified
                                                                        }
                                                                        submitUrl={
                                                                            submitUrl
                                                                        }
                                                                        dataProto={
                                                                            dataProto
                                                                        }
                                                                        orgSettingsData={
                                                                            orgSettingsData
                                                                        }
                                                                        timeFormatMsg
                                                                    />
                                                                )}
                                                            </>
                                                        )}
                                                    </>
                                                )}
                                            </>
                                        )}
                                    </div>
                                    {/* <div
                                        style={{
                                            marginTop: 16,
                                            paddingTop: 16,
                                            borderTop: '1px solid #f0f0f0',
                                        }}
                                    ></div> */}
                                </div>
                            ) : (
                                <BulkUploader
                                    onDataModified={onDataModified}
                                    submitUrl={submitUrl}
                                    dataProto={dataProto}
                                    orgSettingsData={orgSettingsData}
                                    timeFormatMsg
                                />
                            )}
                        </div>
                        <>
                            {bookingModeModal && (
                                <BookingMode
                                    showBulkBookingModal={bookingModeModal}
                                    onClose={() => {
                                        this.setState({
                                            bookingModeModal: false,
                                        });
                                    }}
                                    capacityData={capacityData}
                                    selectedPrvdr={selectedBulkPrvdr}
                                    srvcTypeId={srvcTypeId}
                                    dataProto={dataProto}
                                    orgSettingsData={orgSettingsData}
                                    onDataModified={onDataModified}
                                    vertical_id={
                                        viewData?.sp_config_data[0]?.db_id
                                    }
                                />
                            )}
                        </>
                    </Collapse.Panel>
                </Collapse>
            </Col>
        );
    }
}

export default BulkCreationCollapse;
